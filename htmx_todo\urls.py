"""
项目级URL配置文件

这个文件是Django项目的主要URL配置文件，负责将URL请求路由到相应的视图函数。

URL路由配置示例:
1. 函数视图配置
   - 导入: from myapp import views
   - 配置: path('', views.home, name='home')
   - 说明: 将根路径映射到views.home函数

2. 类视图配置
   - 导入: from myapp.views import HomeView
   - 配置: path('', HomeView.as_view(), name='home')
   - 说明: 将根路径映射到HomeView类视图

3. 包含其他URL配置
   - 导入: from django.urls import include
   - 配置: path('blog/', include('blog.urls'))
   - 说明: 将blog/开头的请求转发到blog应用的urls.py
"""

# 导入必要的Django组件
from django.contrib import admin  # 导入admin模块，用于Django管理后台
from django.urls import path, include  # 导入path和include函数用于URL配置

# 定义URL模式列表
urlpatterns = [
    # Django管理后台路由
    # 访问/admin/时将请求转发到Django的管理界面
    path("admin/", admin.site.urls),
    
    # 应用主路由
    # 将根路径的请求转发到todos应用的urls.py
    # 这样todos应用可以处理所有非admin的URL请求
    path("", include("todos.urls")),
]
