# 导入Django的模型类和时区工具
from django.db import models
from django.utils import timezone


class Todo(models.Model):
    """
    待办事项模型类
    用于存储和管理待办事项的数据结构
    """
    # 待办事项标题，最大长度200字符
    title = models.CharField(max_length=200)
    
    # 完成状态，默认为False（未完成）
    completed = models.BooleanField(default=False)
    
    # 创建时间，默认为当前时间（使用时区感知的datetime）
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        """
        模型的元数据类
        """
        # 设置默认排序方式：按创建时间降序（'-'表示降序）
        ordering = ['-created_at']

    def __str__(self):
        """
        这个方法的作用是定义模型实例的字符串表示形式。主要用途包括：
        1. 在Django管理后台(Admin)界面中，显示每条记录时会调用这个方法
        2. 在Python交互式命令行中打印对象时会显示这个返回值
        3. 在调试时，打印对象会显示这个返回值，便于识别具体是哪条记录
        4. 在其他需要将对象转换为字符串的场景下使用
        
        例如：当你在Admin后台查看Todo列表时，每行都会显示对应的title，
        而不是显示"Todo object (1)"这样的默认形式
        """
        return self.title
