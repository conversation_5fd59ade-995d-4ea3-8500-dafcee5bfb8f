<!-- 这是待办事项应用的基础模板文件 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 基本的meta标签设置 -->
    <meta charset="UTF-8">  <!-- 设置字符编码为UTF-8 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">  <!-- 响应式设计viewport设置 -->
    <title>HTMX 待办事项</title>  <!-- 页面标题 -->
    
    <!-- 引入Bootstrap CSS框架，用于页面样式和布局 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- 引入Bootstrap Icons图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet" crossorigin="anonymous">

    <!-- 引入HTMX库，用于实现无刷新页面更新 -->
    <script src="https://unpkg.com/htmx.org@1.9.10" crossorigin="anonymous"></script>

    <!-- HTMX的CSRF保护配置 -->
    <script>
        // 获取CSRF令牌的函数
        function getCSRFToken() {
            return document.querySelector('[name=csrfmiddlewaretoken]').value;
        }

        // 为所有HTMX请求添加CSRF令牌
        document.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-CSRFToken'] = getCSRFToken();
        });
    </script>
    
    <!-- 优化后的CSS样式 - 减少复杂效果提升性能 -->
    <style>
        /* 页面整体样式 - 简化背景 */
        body {
            background: #f8f9fa;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* 主容器样式 - 简化效果 */
        .container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        /* 已完成任务的样式 */
        .completed {
            text-decoration: line-through;
            opacity: 0.6;
        }

        /* HTMX加载指示器 - 简化动画 */
        .htmx-indicator {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .htmx-request .htmx-indicator {
            opacity: 1;
        }

        /* 简化的悬停效果 */
        .todo-item-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        /* 移除过度的动画效果 */
        .todo-item-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
    </style>
</head>
<body>
    <!-- Bootstrap容器，用于居中内容和添加边距 -->
    <div class="container py-4">
        <!-- Bootstrap栅格系统，用于响应式布局 -->
        <div class="row justify-content-center">
            <!-- 在中等屏幕上占8列宽度，大屏幕占6列 -->
            <div class="col-12 col-md-8 col-lg-6">
                <!-- Django模板块，用于子模板继承和内容填充 -->
                {% block content %}
                {% endblock %}
            </div>
        </div>
    </div>
</body>
</html>
