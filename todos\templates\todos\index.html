{% extends 'todos/base.html' %}

{% block content %}
<!-- 隐藏的CSRF令牌，供JavaScript使用 -->
{% csrf_token %}

<!-- 页面标题区域 -->
<div class="text-center mb-5">
    <h1 class="display-4 fw-bold text-primary mb-3">
        <i class="bi bi-check2-square me-3"></i>HTMX 待办事项
    </h1>
    <p class="lead text-muted">简单高效的任务管理工具</p>
</div>

<!-- 添加新待办事项的表单 - 使用HTMX -->
<div class="card border-0 shadow-sm mb-4 bg-primary">
    <div class="card-body p-4">
        <h5 class="card-title text-white mb-3">
            <i class="bi bi-plus-circle me-2"></i>添加新任务
        </h5>
        <form hx-post="{% url 'todos:add_todo' %}"
              hx-target="#todo-list"
              hx-swap="afterbegin"
              hx-on::after-request="this.reset()">
            {% csrf_token %}
            <div class="input-group input-group-lg">
                <span class="input-group-text bg-white border-0">
                    <i class="bi bi-pencil text-primary"></i>
                </span>
                <input type="text"
                       class="form-control border-0 shadow-sm"
                       name="title"
                       placeholder="输入新的待办事项..."
                       required
                       >
                <button type="submit" class="btn btn-light btn-lg px-4 fw-medium border-0 shadow-sm">
                    <span class="htmx-indicator spinner-border spinner-border-sm me-2"></span>
                    <i class="bi bi-plus-lg me-1"></i>添加任务
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 待办事项列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-0 py-3">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="card-title mb-0 text-dark fw-bold">
                <i class="bi bi-list-task me-2 text-primary"></i>我的任务列表
            </h5>
            <span class="badge bg-primary-subtle text-primary px-3 py-2 rounded-pill">
                <i class="bi bi-collection me-1"></i>{{ todos|length }} 个任务
            </span>
        </div>
    </div>
    <div class="card-body p-4 bg-light">
        <div id="todo-list">
            {% for todo in todos %}
                {% include 'todos/todo_item.html' %}
            {% empty %}
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                    </div>
                    <h6 class="text-muted mb-2">还没有任务</h6>
                    <p class="text-muted small">添加一个新任务开始管理你的待办事项吧！</p>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- HTMX 功能说明卡片 -->
<div class="mt-4 card border-0 shadow-sm">
    <div class="card-body p-4 bg-info">
        <h6 class="text-white mb-3">
            <i class="bi bi-lightning-charge me-2"></i>HTMX 功能演示
        </h6>
        <div class="row g-3">
            <div class="col-md-6">
                <div class="d-flex align-items-center text-white">
                    <i class="bi bi-plus-circle me-2"></i>
                    <small><strong>添加任务</strong>：表单提交后无需刷新页面</small>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center text-white">
                    <i class="bi bi-check-square me-2"></i>
                    <small><strong>切换状态</strong>：点击复选框实时更新</small>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center text-white">
                    <i class="bi bi-trash me-2"></i>
                    <small><strong>删除任务</strong>：点击删除按钮立即移除</small>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center text-white">
                    <i class="bi bi-code-slash me-2"></i>
                    <small><strong>无JavaScript</strong>：所有交互都通过HTMX属性实现</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
