<div class="todo-item-card mb-3 p-3 {% if todo.completed %}bg-light{% else %}bg-white{% endif %} rounded-3 shadow-sm border-0 position-relative"
     id="todo-{{ todo.id }}">

    <!-- 左侧装饰条 -->
    <div class="position-absolute start-0 top-0 h-100 {% if todo.completed %}bg-success{% else %}bg-primary{% endif %}"
         style="width: 4px; opacity: 0.8;"></div>

    <!-- 任务内容区域 -->
    <div class="d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center flex-grow-1">
            <!-- 自定义复选框 -->
            <div class="form-check me-3">
                <input type="checkbox"
                       class="form-check-input fs-5 {% if todo.completed %}bg-success border-success{% else %}border-primary{% endif %}"
                       {% if todo.completed %}checked{% endif %}
                       hx-post="{% url 'todos:toggle_todo' todo.id %}"
                       hx-target="#todo-{{ todo.id }}"
                       hx-swap="outerHTML"
                       style="cursor: pointer;">
            </div>

            <!-- 任务标题和状态 -->
            <div class="flex-grow-1">
                <div class="d-flex align-items-center">
                    <!-- 任务标题 -->
                    <span class="{% if todo.completed %}completed text-muted{% else %}text-dark fw-medium{% endif %} fs-6 me-2"
                          >
                        {{ todo.title }}
                    </span>

                    <!-- 状态标签 -->
                    {% if todo.completed %}
                        <span class="badge bg-success-subtle text-success border border-success-subtle px-2 py-1 rounded-pill">
                            <i class="bi bi-check-circle-fill me-1"></i>已完成
                        </span>
                    {% else %}
                        <span class="badge bg-warning-subtle text-warning border border-warning-subtle px-2 py-1 rounded-pill">
                            <i class="bi bi-clock me-1"></i>进行中
                        </span>
                    {% endif %}
                </div>

                <!-- 创建时间 -->
                <small class="text-muted d-block mt-1">
                    <i class="bi bi-calendar3 me-1"></i>创建于 {{ todo.created_at|date:"m月d日 H:i" }}
                </small>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="d-flex align-items-center ms-3">
            <!-- 删除按钮 -->
            <button class="btn btn-outline-danger btn-sm rounded-pill px-3 py-2"
                    hx-delete="{% url 'todos:delete_todo' todo.id %}"
                    hx-target="#todo-{{ todo.id }}"
                    hx-swap="outerHTML"
                    title="点击删除任务">
                <span class="htmx-indicator spinner-border spinner-border-sm me-1"></span>
                <i class="bi bi-trash3 me-1"></i>删除
            </button>
        </div>
    </div>

</div>
