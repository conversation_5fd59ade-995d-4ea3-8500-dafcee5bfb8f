# 导入Django的URL路由模块
from django.urls import path
# 从当前目录导入views模块
from . import views

# 定义应用的命名空间，避免URL名称冲突
# app_name的命名讲究:
# 1. 必须是有效的Python标识符(字母、数字、下划线,不能以数字开头)
# 2. 通常使用应用的名称作为命名空间,保持一致性
# 3. 使用小写字母,避免使用大写(Django约定)
# 4. 名称要具有描述性,反映应用的功能
# 5. 避免与Django内置命名空间冲突(如admin、auth等)
app_name = 'todos'  # 使用应用名作为URL命名空间

# 定义URL模式列表
urlpatterns = [
    # 主页路由 - 匹配空路径
    # 当访问根URL时，调用views.index视图函数
    path('', views.index, name='index'),
    
    # 添加待办事项路由
    # 处理/add/路径，调用views.add_todo视图函数
    path('add/', views.add_todo, name='add_todo'),
    
    # 切换待办事项状态路由
    # 处理/toggle/数字/路径，如/toggle/1/
    # <int:todo_id>是URL参数，会被传递给视图函数
    path('toggle/<int:todo_id>/', views.toggle_todo, name='toggle_todo'),
    
    # 删除待办事项路由
    # 处理/delete/数字/路径，如/delete/1/
    # 用于删除指定ID的待办事项
    path('delete/<int:todo_id>/', views.delete_todo, name='delete_todo'),
]
