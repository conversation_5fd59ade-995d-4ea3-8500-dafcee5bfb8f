# 导入必要的Django模块
from django.shortcuts import render, get_object_or_404  # 用于渲染模板和获取对象（不存在则返回404）
from django.http import HttpResponse  # 用于创建HTTP响应
from .models import Todo  # 导入Todo模型


def index(request):
    """
    主页视图函数
    功能：显示所有待办事项列表
    参数：request - HTTP请求对象
    返回：渲染后的index.html模板
    """
    # 优化：限制查询数量，避免加载过多数据
    todos = Todo.objects.all()[:100]  # 只获取最新的100条待办事项
    return render(request, 'todos/index.html', {'todos': todos})  # 渲染模板并传递数据


def add_todo(request):
    """
    添加待办事项的HTMX端点
    功能：处理POST请求，创建新的待办事项
    参数：
        request - HTTP请求对象
    返回：
        成功 - 返回新创建的待办事项HTML片段
        失败 - 返回空响应
    """
    if request.method == 'POST':  # 确保是POST请求
        # 从POST请求中获取title字段的值
        # get()方法的第二个参数''是默认值，如果找不到title字段则返回空字符串
        # strip()方法用于去除字符串首尾的空白字符(空格、换行符等)
        title = request.POST.get('title', '').strip()
        if title:  # 确保标题不为空
            todo = Todo.objects.create(title=title)  # 创建新的待办事项
            # 返回新创建的待办事项的HTML片段（将被HTMX插入到页面中）
            return render(request, 'todos/todo_item.html', {'todo': todo})
    return HttpResponse('')  # 如果标题为空或非POST请求，返回空响应


def toggle_todo(request, todo_id):
    """
    切换待办事项完成状态的HTMX端点
    功能：处理POST请求，切换指定待办事项的完成状态
    参数：
        request - HTTP请求对象
        todo_id - 待办事项的ID
    返回：
        成功 - 返回更新后的待办事项HTML片段
        失败 - 返回405状态码
    """
    if request.method == 'POST':  # 确保是POST请求
        todo = get_object_or_404(Todo, id=todo_id)  # 获取待办事项，不存在则返回404
        todo.completed = not todo.completed  # 切换完成状态
        todo.save()  # 保存更改到数据库
        # 返回更新后的待办事项HTML片段（将被HTMX用来替换原有内容）
        return render(request, 'todos/todo_item.html', {'todo': todo})
    return HttpResponse('Method not allowed', status=405)  # 非POST请求返回405错误


def delete_todo(request, todo_id):
    """
    删除待办事项的HTMX端点
    功能：处理DELETE请求，删除指定的待办事项
    参数：
        request - HTTP请求对象
        todo_id - 待办事项的ID
    返回：
        成功 - 返回空响应（HTMX会移除对应的DOM元素）
        失败 - 返回405状态码
    """
    if request.method == 'DELETE':  # 确保是DELETE请求
        todo = get_object_or_404(Todo, id=todo_id)  # 获取待办事项，不存在则返回404
        todo.delete()  # 从数据库中删除该待办事项
        # 返回空响应，HTMX会自动移除对应的DOM元素
        return HttpResponse('')
    return HttpResponse('Method not allowed', status=405)  # 非DELETE请求返回405错误
